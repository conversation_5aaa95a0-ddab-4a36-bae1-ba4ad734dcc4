'use client';

import React, { createContext, useContext, useEffect, useRef, useState, useCallback } from 'react';
import { Centrifuge, Subscription } from 'centrifuge';

interface CentrifugoMessage {
  type: 'message' | 'conversation_update' | 'typing' | 'presence';
  data: any;
  timestamp: number;
}

interface CentrifugoContextType {
  isConnected: boolean;
  subscribe: (channel: string, callbacks: ChannelCallbacks) => () => void;
  publish: (channel: string, data: any) => void;
  reconnect: () => void;
}

interface ChannelCallbacks {
  onMessage?: (data: any) => void;
  onSubscribed?: () => void;
  onUnsubscribed?: () => void;
  onError?: (error: any) => void;
}

const CentrifugoContext = createContext<CentrifugoContextType | null>(null);

interface CentrifugoProviderProps {
  children: React.ReactNode;
  userId: string;
}

export function CentrifugoProvider({ children, userId }: CentrifugoProviderProps) {
  const [isConnected, setIsConnected] = useState(false);
  const centrifugeRef = useRef<Centrifuge | null>(null);
  const subscriptionsRef = useRef<Map<string, Subscription>>(new Map());
  const isConnectingRef = useRef(false);
  const subscriptionCounterRef = useRef<Map<string, number>>(new Map());

  const getToken = useCallback(async (): Promise<string> => {
    try {
      const response = await fetch('/api/v1/centrifugo/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          conversationIds: [] // Global token, no specific conversations
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get Centrifugo token');
      }

      const data = await response.json();
      return data.token;
    } catch (error) {
      console.error('Error getting Centrifugo token:', error);
      throw error;
    }
  }, [userId]);

  const connect = useCallback(async () => {
    if (isConnectingRef.current || centrifugeRef.current) {
      console.log('Already connecting or connected to Centrifugo');
      return;
    }

    isConnectingRef.current = true;
    console.log('Establishing global Centrifugo connection...');

    try {
      const token = await getToken();

      const centrifuge = new Centrifuge(
        process.env.NEXT_PUBLIC_CENTRIFUGO_WS_URL || 'ws://localhost:8001/connection/websocket',
        {
          token,
          debug: process.env.NODE_ENV === 'development',
        }
      );

      centrifuge.on('connecting', () => {
        console.log('🔄 Connecting to Centrifugo...');
      });

      centrifuge.on('connected', () => {
        console.log('✅ Connected to Centrifugo globally');
        setIsConnected(true);
        isConnectingRef.current = false;
      });

      centrifuge.on('disconnected', () => {
        console.log('❌ Disconnected from Centrifugo');
        setIsConnected(false);
        isConnectingRef.current = false;

        // Clear all subscriptions and counters on disconnect
        subscriptionsRef.current.clear();
        subscriptionCounterRef.current.clear();
      });

      centrifuge.on('error', (error) => {
        console.error('Centrifugo connection error:', error);
        isConnectingRef.current = false;
      });

      centrifugeRef.current = centrifuge;
      centrifuge.connect();
    } catch (error) {
      console.error('Failed to establish Centrifugo connection:', error);
      isConnectingRef.current = false;
    }
  }, [getToken]);

  const subscribe = useCallback((channel: string, callbacks: ChannelCallbacks) => {
    if (!centrifugeRef.current || !isConnected) {
      console.warn(`Cannot subscribe to ${channel}: not connected`);
      return () => {}; // Return empty unsubscribe function
    }

    // Check if we already have a subscription to this channel
    const existingSubscription = subscriptionsRef.current.get(channel);
    if (existingSubscription) {
      // Increment reference count
      const currentCount = subscriptionCounterRef.current.get(channel) || 0;
      subscriptionCounterRef.current.set(channel, currentCount + 1);
      console.log(`📈 Reusing existing subscription for ${channel} (refs: ${currentCount + 1})`);

      // Return unsubscribe function that decrements counter
      return () => {
        const refCount = subscriptionCounterRef.current.get(channel) || 0;
        if (refCount > 1) {
          subscriptionCounterRef.current.set(channel, refCount - 1);
          console.log(`📉 Decremented reference for ${channel} (refs: ${refCount - 1})`);
        } else {
          // Last reference, actually unsubscribe
          subscriptionCounterRef.current.delete(channel);
          if (subscriptionsRef.current.has(channel)) {
            try {
              existingSubscription.unsubscribe();
            } catch (error) {
              console.error(`Error unsubscribing from ${channel}:`, error);
            }
            subscriptionsRef.current.delete(channel);
            console.log(`🔌 Unsubscribed from ${channel} (last reference)`);
          }
        }
      };
    }

    console.log(`📡 Creating new subscription for ${channel}...`);

    let subscription;
    try {
      subscription = centrifugeRef.current.newSubscription(channel);
    } catch (error) {
      console.error(`Error creating subscription for ${channel}:`, error);
      return () => {};
    }

    subscription.on('publication', (ctx: any) => {
      const message: CentrifugoMessage = ctx.data;
      callbacks.onMessage?.(message);
    });

    subscription.on('subscribed', () => {
      console.log(`✅ Subscribed to ${channel}`);
      callbacks.onSubscribed?.();
    });

    subscription.on('unsubscribed', () => {
      console.log(`❌ Unsubscribed from ${channel}`);
      subscriptionsRef.current.delete(channel);
      subscriptionCounterRef.current.delete(channel);
      callbacks.onUnsubscribed?.();
    });

    subscription.on('error', (error: any) => {
      console.error(`Subscription error for ${channel}:`, error);
      subscriptionsRef.current.delete(channel);
      subscriptionCounterRef.current.delete(channel);
      callbacks.onError?.(error);
    });

    subscriptionsRef.current.set(channel, subscription);
    subscriptionCounterRef.current.set(channel, 1);

    try {
      subscription.subscribe();
    } catch (error) {
      console.error(`Error subscribing to ${channel}:`, error);
      subscriptionsRef.current.delete(channel);
      subscriptionCounterRef.current.delete(channel);
      return () => {};
    }

    // Return unsubscribe function
    return () => {
      const refCount = subscriptionCounterRef.current.get(channel) || 0;
      if (refCount > 1) {
        subscriptionCounterRef.current.set(channel, refCount - 1);
        console.log(`📉 Decremented reference for ${channel} (refs: ${refCount - 1})`);
      } else {
        // Last reference, actually unsubscribe
        subscriptionCounterRef.current.delete(channel);
        if (subscriptionsRef.current.has(channel) && subscriptionsRef.current.get(channel) === subscription) {
          try {
            subscription.unsubscribe();
          } catch (error) {
            console.error(`Error unsubscribing from ${channel}:`, error);
          }
          subscriptionsRef.current.delete(channel);
          console.log(`🔌 Unsubscribed from ${channel} (last reference)`);
        }
      }
    };
  }, [isConnected]);

  const publish = useCallback((channel: string, data: any) => {
    const subscription = subscriptionsRef.current.get(channel);
    if (subscription) {
      subscription.publish(data);
    } else {
      console.warn(`Cannot publish to ${channel}: not subscribed`);
    }
  }, []);

  const reconnect = useCallback(() => {
    console.log('🔄 Reconnecting to Centrifugo...');

    // Clear all subscriptions and counters
    subscriptionsRef.current.forEach((subscription) => {
      try {
        subscription.unsubscribe();
      } catch (error) {
        console.error('Error unsubscribing during reconnect:', error);
      }
    });
    subscriptionsRef.current.clear();
    subscriptionCounterRef.current.clear();

    // Disconnect and reconnect
    if (centrifugeRef.current) {
      try {
        centrifugeRef.current.disconnect();
      } catch (error) {
        console.error('Error disconnecting during reconnect:', error);
      }
      centrifugeRef.current = null;
      setIsConnected(false);
    }

    setTimeout(() => {
      connect();
    }, 1000);
  }, [connect]);

  // Initialize connection on mount
  useEffect(() => {
    connect();

    return () => {
      console.log('🧹 Cleaning up global Centrifugo connection...');

      // Unsubscribe from all channels
      subscriptionsRef.current.forEach((subscription) => {
        try {
          subscription.unsubscribe();
        } catch (error) {
          console.error('Error unsubscribing during cleanup:', error);
        }
      });
      subscriptionsRef.current.clear();
      subscriptionCounterRef.current.clear();

      // Disconnect
      if (centrifugeRef.current) {
        try {
          centrifugeRef.current.disconnect();
        } catch (error) {
          console.error('Error disconnecting during cleanup:', error);
        }
        centrifugeRef.current = null;
        setIsConnected(false);
      }

      isConnectingRef.current = false;
    };
  }, [connect]);

  const contextValue: CentrifugoContextType = {
    isConnected,
    subscribe,
    publish,
    reconnect,
  };

  return (
    <CentrifugoContext.Provider value={contextValue}>
      {children}
    </CentrifugoContext.Provider>
  );
}

export function useCentrifugoContext() {
  const context = useContext(CentrifugoContext);
  if (!context) {
    throw new Error('useCentrifugoContext must be used within a CentrifugoProvider');
  }
  return context;
}
