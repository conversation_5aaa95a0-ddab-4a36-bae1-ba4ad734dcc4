'use client';

import { CentrifugoProvider } from '@/app/_contexts/CentrifugoContext';

interface AppProvidersProps {
  children: React.ReactNode;
}

export function AppProviders({ children }: AppProvidersProps) {
  // TODO: Replace with actual user ID from authentication context
  // For now, using a static user ID
  const userId = 'current-user-id';

  return (
    <CentrifugoProvider userId={userId}>
      {children}
    </CentrifugoProvider>
  );
}
