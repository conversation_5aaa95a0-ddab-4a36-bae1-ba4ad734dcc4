import { NextResponse } from "next/server";
import {$Enums, PrismaClient} from '@/../generated/prisma';
import twilio from 'twilio';
import { formatToE164 } from "@/components/phoneNumber/phoneFormatters";
import { generateAgentResponse, getResponseDelayMs } from "@/app/api/v1/_components/agent";
import { findOrCreateLead, findOrCreateConversation } from "@/app/api/v1/_components/lead";
import { broadcastMessage } from "@/app/api/v1/_components/centrifugo";
import { shouldSendAutomatedResponse } from "@/app/api/v1/_components/conversation/conversation-service";
import CompanySize = $Enums.CompanySize;

const prisma = new PrismaClient();

// Initialize Twilio client
const twilioClient = twilio(
    process.env.TWILIO_ACCOUNT_SID,
    process.env.TWILIO_AUTH_TOKEN
);

export async function POST(request: Request, { params }: { params: { id: string } }) {
  try {
    const { id } = await params;

    // Parse the incoming form data from Twilio
    const formData = await request.formData();
    const from = formData.get('From') as string;
    const to = formData.get('To') as string;
    const body = formData.get('Body') as string;
    const messageSid = formData.get('MessageSid') as string;



    console.log(`Received SMS from ${from} to ${to}: ${body}`);

    // Find the agent by ID
    const agent = await prisma.agent.findUnique({
      where: {
        id: id
      },
      include: {
        client: {
          include: {
            business_type: true
          }
        },
        phone_number: true,
        auto_responses: {
          orderBy: {
            created_at: 'desc',
          },
          take: 1,
        },
      },
    });

    if (!agent) {
      console.error(`Agent not found with ID ${id}`);
      return NextResponse.json(
          { error: "Agent not found" },
          { status: 404 }
      );
    }

    const autoResponse = agent.auto_responses[0];

    if (!autoResponse || !autoResponse.enabled) {
      console.log(`Auto-response is disabled for agent ${agent.id}`);
      return NextResponse.json(
          { message: "Auto-response is disabled" },
          { status: 200 }
      );
    }

    // Find or create lead using the reusable component
    const leadResult = await findOrCreateLead({
      phoneNumber: from,
      clientId: agent.client_id,
      agentId: agent.id,
    });

    const lead = leadResult.lead;

    // Find or create conversation using the reusable component
    const conversationResult = await findOrCreateConversation({
      agentId: agent.id,
      leadId: lead.id,
      clientId: agent.client_id,
    });

    const conversation = conversationResult.conversation;

    // Store the incoming message (always store incoming messages regardless of agent status)
    const incomingMessage = await prisma.message.create({
      data: {
        conversation_id: conversation.id,
        client_id: agent.client_id,
        phone_number_id: lead.phone_number_id,
        body: body,
        status: 'delivered',
        direction: 'INBOUND',
        is_read: false,
        cost: 0.0 // You might want to calculate this based on Twilio's pricing
      }
    });

    // Broadcast incoming message to Centrifugo
    try {
      await broadcastMessage({
        conversationId: conversation.id,
        message: {
          id: incomingMessage.id,
          conversation_id: conversation.id,
          body: body,
          direction: 'INBOUND',
          created_at: incomingMessage.created_at.toISOString(),
          sender: {
            id: lead.person?.id || lead.id,
            name: lead.person?.name || formatToE164(lead.phone_number.number),
            phone_number: from,
            type: 'lead'
          }
        }
      });
    } catch (centrifugoError) {
      console.error('Failed to broadcast incoming message to Centrifugo:', centrifugoError);
      // Continue processing even if Centrifugo broadcast fails
    }

    // Check if automated responses should be sent for this conversation
    const shouldRespond = await shouldSendAutomatedResponse(conversation.id);

    if (!shouldRespond) {
      console.log(`Automated responses disabled for conversation ${conversation.id}`);
      return NextResponse.json({
        message: "Message received but automated response is disabled",
        conversation_id: conversation.id,
        agent_active: false
      });
    }

    // Generate AI response using the reusable component
    const aiResult = await generateAgentResponse({
      agentId: id,
      scenario: 'sms',
      userMessage: body,
      conversationHistory: {
        id: conversation.id,
        messages: conversation.messages
      },
      leadContext: {
        id: lead.id,
        phone_number_id: lead.phone_number_id,
        name: lead.person?.name || undefined
      }
    });

    const aiResponse = aiResult.response;

    // Apply response delay if configured
    if (autoResponse.response_delay !== 'IMMEDIATE') {
      const delayMs = getResponseDelayMs(autoResponse.response_delay);
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }

    // Send the response via Twilio
    const twilioResponse = await twilioClient.messages.create({
      body: aiResponse,
      from: to,
      to: from,
    });

    // Store the outgoing message with agent creator
    const outgoingMessage = await prisma.message.create({
      data: {
        conversation_id: conversation.id,
        client_id: agent.client_id,
        phone_number_id: agent.phone_number_id,
        body: aiResponse,
        status: 'delivered',
        direction: 'OUTBOUND',
        is_read: true,
        created_by_agent: agent.id, // Mark as created by agent
        cost: 0.0 // You might want to calculate this based on Twilio's pricing
      }
    });

    // Broadcast outgoing message to Centrifugo
    try {
      await broadcastMessage({
        conversationId: conversation.id,
        message: {
          id: outgoingMessage.id,
          conversation_id: conversation.id,
          body: aiResponse,
          direction: 'OUTBOUND',
          created_at: outgoingMessage.created_at.toISOString(),
          sender: {
            id: agent.id,
            name: agent.label || 'Agent',
            phone_number: to,
            type: 'agent'
          },
          created_by_agent: agent.id,
          created_by_person: null
        }
      });
    } catch (centrifugoError) {
      console.error('Failed to broadcast outgoing message to Centrifugo:', centrifugoError);
      // Continue processing even if Centrifugo broadcast fails
    }

    console.log(`Sent response to ${from}: ${aiResponse}`);

    // Return a TwiML response (though Twilio doesn't require it for webhook)
    return new NextResponse(
        `<?xml version="1.0" encoding="UTF-8"?>
      <Response></Response>`,
        {
          status: 200,
          headers: {
            "Content-Type": "text/xml",
          },
        }
    );

  } catch (error) {
    console.error("Error processing SMS webhook:", error);
    return NextResponse.json(
        { error: "Failed to process SMS webhook" },
        { status: 500 }
    );
  }
}


