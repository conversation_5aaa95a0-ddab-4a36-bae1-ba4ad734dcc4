'use client';

import { useEffect, useState, useCallback, useRef } from 'react';
import { useCentrifugoContext } from '@/app/_contexts/CentrifugoContext';

interface UseCentrifugoSubscriptionOptions {
  channel: string;
  onMessage?: (data: any) => void;
  enabled?: boolean;
}

interface UseCentrifugoSubscriptionReturn {
  isSubscribed: boolean;
  publish: (data: any) => void;
}

/**
 * Generic hook for subscribing to any Centrifugo channel
 * Use this for custom channels beyond conversations
 */
export function useCentrifugoSubscription(
  options: UseCentrifugoSubscriptionOptions
): UseCentrifugoSubscriptionReturn {
  const { channel, onMessage, enabled = true } = options;
  const { isConnected, subscribe, publish: contextPublish } = useCentrifugoContext();
  const [isSubscribed, setIsSubscribed] = useState(false);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  // Store callback ref to avoid dependency issues
  const callbackRef = useRef(onMessage);

  // Update callback ref when it changes
  useEffect(() => {
    callbackRef.current = onMessage;
  }, [onMessage]);

  const handleMessage = useCallback((message: any) => {
    callbackRef.current?.(message);
  }, []);

  const publishToChannel = useCallback((data: any) => {
    if (!isSubscribed) {
      console.warn(`Cannot publish to ${channel}: not subscribed`);
      return;
    }
    contextPublish(channel, data);
  }, [channel, isSubscribed, contextPublish]);

  // Subscribe to channel when connected and enabled
  useEffect(() => {
    if (!isConnected || !enabled || !channel) {
      setIsSubscribed(false);
      return;
    }

    // Clean up previous subscription if exists
    if (unsubscribeRef.current) {
      unsubscribeRef.current();
      unsubscribeRef.current = null;
    }

    console.log(`Setting up subscription for channel: ${channel}`);

    const unsubscribe = subscribe(channel, {
      onMessage: handleMessage,
      onSubscribed: () => {
        console.log(`✅ Subscribed to channel: ${channel}`);
        setIsSubscribed(true);
      },
      onUnsubscribed: () => {
        console.log(`❌ Unsubscribed from channel: ${channel}`);
        setIsSubscribed(false);
      },
      onError: (error) => {
        console.error(`Subscription error for channel ${channel}:`, error);
        setIsSubscribed(false);
      }
    });

    unsubscribeRef.current = unsubscribe;

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
      setIsSubscribed(false);
    };
  }, [isConnected, enabled, channel, subscribe, handleMessage]);

  return {
    isSubscribed,
    publish: publishToChannel,
  };
}
