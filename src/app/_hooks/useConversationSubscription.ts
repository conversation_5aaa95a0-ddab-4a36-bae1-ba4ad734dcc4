'use client';

import { useEffect, useState, useCallback, useRef } from 'react';
import { useCentrifugoContext } from '@/app/_contexts/CentrifugoContext';

interface CentrifugoMessage {
  type: 'message' | 'conversation_update' | 'typing' | 'presence';
  data: any;
  timestamp: number;
}

interface MessageData {
  id: string;
  conversation_id: string;
  body: string;
  direction: 'INBOUND' | 'OUTBOUND';
  created_at: string;
  sender?: {
    id: string;
    name?: string;
    phone_number?: string;
  };
}

interface UseConversationSubscriptionOptions {
  conversationId: string;
  onMessage?: (message: MessageData) => void;
  onConversationUpdate?: (update: any) => void;
  onTyping?: (data: { user_id: string; is_typing: boolean }) => void;
  onPresence?: (data: { user_id: string; online: boolean }) => void;
}

interface UseConversationSubscriptionReturn {
  isSubscribed: boolean;
  sendTyping: (isTyping: boolean) => void;
}

export function useConversationSubscription(
  options: UseConversationSubscriptionOptions
): UseConversationSubscriptionReturn {
  const {
    conversationId,
    onMessage,
    onConversationUpdate,
    onTyping,
    onPresence
  } = options;

  const { isConnected, subscribe, publish } = useCentrifugoContext();
  const [isSubscribed, setIsSubscribed] = useState(false);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  // Store callback refs to avoid dependency issues
  const callbacksRef = useRef({
    onMessage,
    onConversationUpdate,
    onTyping,
    onPresence
  });

  // Update callbacks ref when they change
  useEffect(() => {
    callbacksRef.current = {
      onMessage,
      onConversationUpdate,
      onTyping,
      onPresence
    };
  }, [onMessage, onConversationUpdate, onTyping, onPresence]);

  const handleMessage = useCallback((message: CentrifugoMessage) => {
    switch (message.type) {
      case 'message':
        callbacksRef.current.onMessage?.(message.data);
        break;
      case 'conversation_update':
        callbacksRef.current.onConversationUpdate?.(message.data);
        break;
      case 'typing':
        callbacksRef.current.onTyping?.(message.data);
        break;
      case 'presence':
        callbacksRef.current.onPresence?.(message.data);
        break;
    }
  }, []);

  const sendTyping = useCallback((isTyping: boolean) => {
    if (!isSubscribed) {
      console.warn('Cannot send typing indicator: not subscribed to conversation');
      return;
    }

    const channel = `conversations:${conversationId}`;
    publish(channel, {
      type: 'typing',
      data: {
        user_id: 'current-user-id', // TODO: Get from auth context
        is_typing: isTyping,
      },
      timestamp: Date.now(),
    });
  }, [conversationId, isSubscribed, publish]);

  // Subscribe to conversation channel when connected and conversationId is available
  useEffect(() => {
    if (!isConnected || !conversationId) {
      setIsSubscribed(false);
      return;
    }

    const channel = `conversations:${conversationId}`;

    // Clean up previous subscription if exists
    if (unsubscribeRef.current) {
      unsubscribeRef.current();
      unsubscribeRef.current = null;
    }

    console.log(`Setting up subscription for conversation: ${conversationId}`);

    const unsubscribe = subscribe(channel, {
      onMessage: handleMessage,
      onSubscribed: () => {
        console.log(`✅ Subscribed to conversation: ${conversationId}`);
        setIsSubscribed(true);
      },
      onUnsubscribed: () => {
        console.log(`❌ Unsubscribed from conversation: ${conversationId}`);
        setIsSubscribed(false);
      },
      onError: (error) => {
        console.error(`Subscription error for conversation ${conversationId}:`, error);
        setIsSubscribed(false);
      }
    });

    unsubscribeRef.current = unsubscribe;

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
      setIsSubscribed(false);
    };
  }, [isConnected, conversationId, subscribe, handleMessage]);

  return {
    isSubscribed,
    sendTyping,
  };
}
