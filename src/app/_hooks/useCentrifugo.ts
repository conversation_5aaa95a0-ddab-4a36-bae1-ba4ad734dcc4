'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { Centrifuge, Subscription } from 'centrifuge';

interface CentrifugoMessage {
  type: 'message' | 'conversation_update' | 'typing' | 'presence';
  data: any;
  timestamp: number;
}

interface MessageData {
  id: string;
  conversation_id: string;
  body: string;
  direction: 'INBOUND' | 'OUTBOUND';
  created_at: string;
  sender?: {
    id: string;
    name?: string;
    phone_number?: string;
  };
}

interface UseCentrifugoOptions {
  conversationId: string;
  userId: string;
  onMessage?: (message: MessageData) => void;
  onConversationUpdate?: (update: any) => void;
  onTyping?: (data: { user_id: string; is_typing: boolean }) => void;
  onPresence?: (data: { user_id: string; online: boolean }) => void;
}

interface UseCentrifugoReturn {
  isConnected: boolean;
  isSubscribed: boolean;
  sendTyping: (isTyping: boolean) => void;
  disconnect: () => void;
  reconnect: () => void;
}

export function useCentrifugo(options: UseCentrifugoOptions): UseCentrifugoReturn {
  const {
    conversationId,
    userId,
    onMessage,
    onConversationUpdate,
    onTyping,
    onPresence
  } = options;

  const [isConnected, setIsConnected] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);

  const centrifugeRef = useRef<Centrifuge | null>(null);
  const subscriptionRef = useRef<Subscription | null>(null);
  const currentChannelRef = useRef<string | null>(null);

  // Store callback refs to avoid dependency issues
  const callbacksRef = useRef({
    onMessage,
    onConversationUpdate,
    onTyping,
    onPresence
  });

  // Update callbacks ref when they change
  useEffect(() => {
    callbacksRef.current = {
      onMessage,
      onConversationUpdate,
      onTyping,
      onPresence
    };
  }, [onMessage, onConversationUpdate, onTyping, onPresence]);

  const getToken = useCallback(async (): Promise<string> => {
    try {
      const response = await fetch('/api/v1/centrifugo/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          conversationIds: [conversationId]
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get Centrifugo token');
      }

      const data = await response.json();
      return data.token;
    } catch (error) {
      console.error('Error getting Centrifugo token:', error);
      throw error;
    }
  }, [userId, conversationId]);

  const connect = useCallback(async () => {
    try {
      const token = await getToken();

      const centrifuge = new Centrifuge(
        process.env.NEXT_PUBLIC_CENTRIFUGO_WS_URL || 'ws://localhost:8001/connection/websocket',
        {
          token,
          debug: process.env.NODE_ENV === 'development',
        }
      );

      centrifuge.on('connecting', () => {
        console.log('Connecting to Centrifugo...');
      });

      centrifuge.on('connected', () => {
        console.log('Connected to Centrifugo');
        setIsConnected(true);
      });

      centrifuge.on('disconnected', () => {
        console.log('Disconnected from Centrifugo');
        setIsConnected(false);
        setIsSubscribed(false);
      });

      centrifuge.on('error', (error) => {
        console.error('Centrifugo error:', error);
      });

      centrifugeRef.current = centrifuge;
      centrifuge.connect();
    } catch (error) {
      console.error('Failed to connect to Centrifugo:', error);
    }
  }, [getToken]);

  const unsubscribe = useCallback(() => {
    if (subscriptionRef.current) {
      console.log(`Unsubscribing from ${currentChannelRef.current}...`);
      subscriptionRef.current.unsubscribe();
      subscriptionRef.current = null;
      currentChannelRef.current = null;
      setIsSubscribed(false);
    }
  }, []);

  const subscribe = useCallback(() => {
    if (!centrifugeRef.current || !isConnected) return;

    const channel = `conversations:${conversationId}`;

    // If already subscribed to the same channel, don't subscribe again
    if (subscriptionRef.current && currentChannelRef.current === channel) {
      console.log(`Already subscribed to ${channel}`);
      return;
    }

    // Unsubscribe from any existing subscription first
    // unsubscribe();

    console.log(`Creating new subscription for ${channel}...`);
    const subscription = centrifugeRef.current.newSubscription(channel);

    subscription.on('publication', (ctx: any) => {
      const message: CentrifugoMessage = ctx.data;

      switch (message.type) {
        case 'message':
          callbacksRef.current.onMessage?.(message.data);
          break;
        case 'conversation_update':
          callbacksRef.current.onConversationUpdate?.(message.data);
          break;
        case 'typing':
          callbacksRef.current.onTyping?.(message.data);
          break;
        case 'presence':
          callbacksRef.current.onPresence?.(message.data);
          break;
      }
    });

    subscription.on('subscribing', () => {
      console.log(`Subscribing to ${channel}...`);
    });

    subscription.on('subscribed', () => {
      console.log(`Subscribed to ${channel}`);
      setIsSubscribed(true);
    });

    subscription.on('unsubscribed', () => {
      console.log(`Unsubscribed from ${channel}`);
      setIsSubscribed(false);
    });

    subscription.on('error', (error: any) => {
      console.error(`Subscription error for ${channel}:`, error);
      // Reset subscription state on error
      setIsSubscribed(false);
      subscriptionRef.current = null;
      currentChannelRef.current = null;
    });

    subscriptionRef.current = subscription;
    currentChannelRef.current = channel;
    subscription.subscribe();
  }, [conversationId, isConnected, unsubscribe]);

  const sendTyping = useCallback((isTyping: boolean) => {
    if (!subscriptionRef.current) return;

    // Publish typing indicator to the channel
    subscriptionRef.current.publish({
      type: 'typing',
      data: {
        user_id: userId,
        is_typing: isTyping,
      },
      timestamp: Date.now(),
    });
  }, [userId]);

  const disconnect = useCallback(() => {
    console.log('Disconnecting from Centrifugo...');

    // Unsubscribe first
    // unsubscribe();

    if (centrifugeRef.current) {
      centrifugeRef.current.disconnect();
      centrifugeRef.current = null;
      setIsConnected(false);
    }

    console.log('Disconnected from Centrifugo');
  }, [unsubscribe]);

  const reconnect = useCallback(() => {
    disconnect();
    setTimeout(() => {
      connect();
    }, 1000);
  }, [disconnect, connect]);

  useEffect(() => {
    connect();

    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  useEffect(() => {
    if (isConnected && !isSubscribed) {
      subscribe();
    }
  }, [isConnected, isSubscribed, subscribe]);

  // Handle conversation ID changes - need to resubscribe to new channel
  useEffect(() => {
    if (isConnected && conversationId) {
      subscribe();
    }
  }, [conversationId, isConnected, subscribe]);

  return {
    isConnected,
    isSubscribed,
    sendTyping,
    disconnect,
    reconnect,
  };
}
