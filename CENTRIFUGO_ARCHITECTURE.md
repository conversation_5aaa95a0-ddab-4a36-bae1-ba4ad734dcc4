# Centrifugo Architecture - Global Connection Pattern

This document describes the new Centrifugo architecture that establishes a single global connection and allows components to subscribe to specific channels as needed.

## Architecture Overview

### Global Connection Pattern
- **Single Connection**: One WebSocket connection is established when the app loads
- **Component Subscriptions**: Individual components subscribe to specific channels
- **Automatic Cleanup**: Subscriptions are automatically cleaned up when components unmount
- **Scalable**: Easy to add new channel types and subscriptions

## Components

### 1. CentrifugoProvider (`src/app/_contexts/CentrifugoContext.tsx`)
The global context that manages the Centrifugo connection.

**Features:**
- Establishes connection on app startup
- Manages global connection state
- Provides subscription management
- Handles reconnection logic
- Automatic cleanup on app unmount

**Usage:**
```tsx
// Wrap your app in the provider (done in layout.tsx)
<CentrifugoProvider userId="user-123">
  {children}
</CentrifugoProvider>
```

### 2. useConversationSubscription (`src/app/_hooks/useConversationSubscription.ts`)
Specialized hook for conversation channel subscriptions.

**Features:**
- Subscribes to `conversations:{conversationId}` channels
- <PERSON><PERSON> message, conversation update, typing, and presence events
- Provides typing indicator functionality
- Automatic subscription management

**Usage:**
```tsx
const { isSubscribed, sendTyping } = useConversationSubscription({
  conversationId: 'conv-123',
  onMessage: (message) => console.log('New message:', message),
  onConversationUpdate: (update) => console.log('Conversation updated:', update),
  onTyping: (data) => console.log('Typing:', data),
  onPresence: (data) => console.log('Presence:', data),
});
```

### 3. useCentrifugoSubscription (`src/app/_hooks/useCentrifugoSubscription.ts`)
Generic hook for subscribing to any Centrifugo channel.

**Features:**
- Subscribe to any channel type
- Generic message handling
- Publishing capabilities
- Enable/disable subscriptions

**Usage:**
```tsx
const { isSubscribed, publish } = useCentrifugoSubscription({
  channel: 'notifications:user-123',
  onMessage: (data) => console.log('Notification:', data),
  enabled: true,
});
```

### 4. useCentrifugoContext (`src/app/_contexts/CentrifugoContext.tsx`)
Access the global Centrifugo context.

**Usage:**
```tsx
const { isConnected, reconnect } = useCentrifugoContext();
```

## Channel Naming Convention

- **Conversations**: `conversations:{conversationId}`
- **User Notifications**: `notifications:{userId}`
- **Agent Status**: `agents:{agentId}`
- **Global Announcements**: `announcements`

## Message Types

### Conversation Messages
```typescript
interface CentrifugoMessage {
  type: 'message' | 'conversation_update' | 'typing' | 'presence';
  data: any;
  timestamp: number;
}
```

## Benefits of This Architecture

### 1. **Performance**
- Single WebSocket connection reduces overhead
- No multiple connection attempts
- Efficient resource usage

### 2. **Reliability**
- Global connection state management
- Automatic reconnection handling
- Proper cleanup prevents memory leaks

### 3. **Scalability**
- Easy to add new channel types
- Components can subscribe independently
- Reusable subscription patterns

### 4. **Developer Experience**
- Simple hook-based API
- Clear separation of concerns
- Type-safe message handling

## Migration from Old Pattern

### Before (Per-Component Connections)
```tsx
// Each component had its own connection
const { isConnected, isSubscribed } = useCentrifugo({
  conversationId,
  userId,
  onMessage: handleMessage,
});
```

### After (Global Connection + Component Subscriptions)
```tsx
// Global connection status
const { isConnected } = useCentrifugoContext();

// Component-specific subscription
const { isSubscribed } = useConversationSubscription({
  conversationId,
  onMessage: handleMessage,
});
```

## Future Enhancements

### 1. **Authentication Integration**
- Integrate with auth context for automatic user ID
- Handle token refresh automatically

### 2. **Offline Support**
- Queue messages when offline
- Replay missed messages on reconnection

### 3. **Channel Groups**
- Subscribe to multiple related channels
- Bulk subscription management

### 4. **Message Persistence**
- Local storage for important messages
- Sync with server on reconnection

## Debugging

### Development Tools
The `CentrifugoTest` component provides debugging capabilities:
- Connection status monitoring
- Token generation testing
- Manual reconnection
- Real-time activity logs

### Console Logging
All Centrifugo operations are logged with emojis for easy identification:
- 🔄 Connecting/Reconnecting
- ✅ Connected/Subscribed
- ❌ Disconnected/Unsubscribed
- 📡 Subscription events
- 🔌 Unsubscription events

## Best Practices

### 1. **Subscription Management**
- Always clean up subscriptions in useEffect return
- Use the `enabled` prop to conditionally subscribe
- Handle subscription errors gracefully

### 2. **Message Handling**
- Use useCallback for message handlers to prevent re-subscriptions
- Store callbacks in refs to avoid dependency issues
- Validate message data before processing

### 3. **Performance**
- Only subscribe when component needs real-time data
- Unsubscribe when component is not visible
- Use React.memo for components that receive frequent updates

### 4. **Error Handling**
- Implement retry logic for failed subscriptions
- Provide user feedback for connection issues
- Log errors for debugging
