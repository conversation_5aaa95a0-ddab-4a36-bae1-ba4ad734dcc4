# Message Sender Display Updates

## Overview
Updated the message display system to properly show sender information and improve message layout with better spacing and visual hierarchy.

## Changes Made

### 1. Enhanced Sender Information Logic
**File: `src/app/api/v1/conversations/[id]/messages/route.ts`**

- **Improved sender detection logic** to distinguish between:
  - **Lead Person**: Messages from the lead (person associated with the lead)
  - **User Person**: Messages from staff/users (different person than the lead)
  - **Agent**: Automated responses from the agent

- **Enhanced Centrifugo broadcasting** with detailed sender information:
  ```typescript
  senderInfo = {
    id: personId,
    name: userPerson?.name || 'User',
    phone_number: conversation.agent.phone_number.number.toString(),
    type: 'user' // or 'lead', 'agent'
  }
  ```

### 2. Updated SMS Webhook
**File: `src/app/api/v1/agents/[id]/webhook/sms/route.ts`**

- **Enhanced incoming message broadcasting** with proper lead person information
- **Improved outbound message broadcasting** for automated responses
- **Added sender type classification** for better UI handling

### 3. UI Message Display Improvements
**File: `src/app/conversations/ConversationDetails.tsx`**

#### Enhanced Message Interface
- Added `created_by_agent` and `created_by_person` fields
- Added `agent` and `person` objects with detailed information
- Updated `MessageData` interface for real-time updates

#### New Sender Name Logic
```typescript
const getSenderName = (message: Message) => {
  if (message.direction === 'INBOUND') {
    // Always from lead
    return conversation?.lead.person?.name || formatPhoneForDisplay(phone);
  } else {
    // Check creator type
    if (message.created_by_agent) return message.agent?.label || 'Agent';
    if (message.created_by_person) {
      // Distinguish between lead person and user person
      if (message.person?.id === conversation?.lead.person?.id) {
        return message.person.name || 'Lead';
      } else {
        return message.person?.name || 'User';
      }
    }
    return 'Agent'; // Fallback
  }
}
```

#### Improved Message Layout
- **Added sender name display** at the bottom of each message bubble
- **Enhanced spacing** between message elements
- **Improved visual hierarchy** with font weights and colors
- **Better spacing** between time and status with `ml-2` margin

#### Message Bubble Structure
```
┌─────────────────────────┐
│ Message body text       │
│                         │
│ Sender Name (bold)      │
│ Time        Status      │
└─────────────────────────┘
```

### 4. Real-time Message Handling
- **Updated real-time message processing** to include creator information
- **Enhanced message object creation** for live updates
- **Improved sender data mapping** from Centrifugo broadcasts

## Display Logic

### Sender Name Display Rules
1. **Incoming Messages (INBOUND)**:
   - Always show lead person name or formatted phone number
   - Example: "John Doe" or "(*************"

2. **Outgoing Messages (OUTBOUND)**:
   - **Agent Messages**: Show agent label or "Agent"
   - **Lead Person Messages**: Show person name or "Lead"
   - **User Person Messages**: Show person name or "User"

### Message Layout Improvements
- **Increased padding**: `px-4 py-3` for better readability
- **Added margin bottom**: `mb-2` for message body
- **Sender name styling**: `font-medium` for emphasis
- **Better spacing**: Between time and status elements
- **Message spacing**: `mb-4` between message bubbles

## Visual Enhancements

### Color Scheme
- **Outbound messages**: Blue background with white text
- **Inbound messages**: Gray background with dark text
- **Sender names**: Slightly lighter color for subtle emphasis
- **Time/status**: Lightest color for secondary information

### Typography
- **Message body**: `text-sm` (14px)
- **Sender name**: `text-xs font-medium` (12px, bold)
- **Time/status**: `text-xs` (12px, regular)

## Testing Scenarios

### 1. Lead Messages
- **Incoming SMS**: Should show lead person name
- **Manual lead reply**: Should show lead person name

### 2. Agent Messages
- **Automated responses**: Should show agent label or "Agent"
- **Manual agent messages**: Should show agent label

### 3. User Messages
- **Staff member messages**: Should show user person name
- **Admin messages**: Should show user person name

### 4. Real-time Updates
- **Live message reception**: Should display correct sender immediately
- **Status updates**: Should maintain proper spacing
- **Multiple message types**: Should handle mixed conversation correctly

## Benefits

1. **Clear Attribution**: Users can easily see who sent each message
2. **Better UX**: Improved visual hierarchy and readability
3. **Professional Layout**: Clean, modern message bubble design
4. **Flexible System**: Handles multiple sender types gracefully
5. **Real-time Accuracy**: Live updates maintain sender information

## Future Enhancements

1. **Avatar Support**: Could add profile pictures for senders
2. **Message Threading**: Group consecutive messages from same sender
3. **Typing Indicators**: Show when someone is typing
4. **Read Receipts**: Enhanced delivery status tracking
5. **Message Reactions**: Add emoji reactions to messages

This implementation provides a robust foundation for message attribution and creates a professional, user-friendly conversation interface.
