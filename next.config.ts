import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  allowedDevOrigins: [
      'http://localhost:3000',
      'http://*************:3000',
      'http://*************:3000',
      '*************',
      'http://back-talk.vertigolabs.org',
      'back-talk.vertigolabs.org',
      '************'
  ],
  redirects: async ()=> {
    return [
        {
          source: '/',
          destination: '/dashboard',
          permanent: true,
        }
    ]
  },
};

export default nextConfig;
